import 'dart:developer';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/services/navigation_service.dart';
import 'package:healo/models/medication_intake_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

/// Global function to handle background messages
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log('Handling a background message: ${message.messageId}');
  log('Background message data: ${message.data}');

  if (message.notification != null) {
    log('Background message notification: ${message.notification!.title}');
  }
}

/// Service class to handle all push notification operations
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirestoreService _firestoreService = FirestoreService();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('Initializing notification service...');

      // Request notification permissions
      await _requestPermissions();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Set up Firebase messaging handlers
      await _setupFirebaseMessaging();

      // Get and store FCM token
      await _handleTokenRefresh();

      _isInitialized = true;
      log('Notification service initialized successfully');
    } catch (e) {
      log('Error initializing notification service: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    log('Requesting notification permissions...');

    // Request permissions for iOS
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    log('Notification permission status: ${settings.authorizationStatus}');

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      log('User granted notification permissions');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      log('User granted provisional notification permissions');
    } else {
      log('User declined or has not accepted notification permissions');
    }
  }

  /// Initialize local notifications for foreground display
  Future<void> _initializeLocalNotifications() async {
    log('Initializing local notifications...');

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/launcher_icon');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// Create notification channel for Android
  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'healo_notifications', // Channel ID
      'Healo Notifications', // Channel name
      description: 'Notifications for medication reminders and health updates',
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    const AndroidNotificationChannel medicationChannel = AndroidNotificationChannel(
      'medication_reminders', // Channel ID
      'Medication Reminders', // Channel name
      description: 'Notifications for medication reminders with action buttons',
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    final androidPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    await androidPlugin?.createNotificationChannel(channel);
    await androidPlugin?.createNotificationChannel(medicationChannel);
  }

  /// Set up Firebase messaging handlers
  Future<void> _setupFirebaseMessaging() async {
    log('Setting up Firebase messaging handlers...');

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log('App opened from terminated state via notification');
      _handleNotificationTap(initialMessage);
    }

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    log('Received foreground message: ${message.messageId}');
    log('Foreground message data: ${message.data}');

    if (message.notification != null) {
      log('Foreground message notification: ${message.notification!.title}');

      // Show local notification for foreground messages
      await _showLocalNotification(message);
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    // Check if this is a medication reminder
    final notificationType = message.data['type'];

    if (notificationType == 'medication_reminder') {
      await _showMedicationNotification(message);
    } else {
      await _showGeneralNotification(message);
    }
  }

  /// Show general notification without actions
  Future<void> _showGeneralNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'healo_notifications',
      'Healo Notifications',
      channelDescription: 'Notifications for medication reminders and health updates',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Healo',
      message.notification?.body ?? 'You have a new notification',
      platformChannelSpecifics,
      payload: message.data.toString(),
    );
  }

  /// Show medication notification with action buttons
  Future<void> _showMedicationNotification(RemoteMessage message) async {
    final medicationName = message.data['medication_name'] ?? 'Medication';
    final time = message.data['time'] ?? '';
    final dosage = message.data['dosage'] ?? '';

    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'medication_reminders',
      'Medication Reminders',
      channelDescription: 'Notifications for medication reminders with action buttons',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      fullScreenIntent: true,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'taken_action',
          'Taken',
          icon: DrawableResourceAndroidBitmap('ic_check'),
          contextual: false, // Set to false for background visibility
          showsUserInterface: false,
        ),
        AndroidNotificationAction(
          'skip_action',
          'Skip',
          icon: DrawableResourceAndroidBitmap('ic_close'),
          contextual: false, // Set to false for background visibility
          showsUserInterface: false,
        ),
      ],
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      categoryIdentifier: 'medication_reminder',
    );

    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Create a better payload format
    final payloadData = {
      'type': 'medication_reminder',
      'medication_name': medicationName,
      'time': time,
      'dosage': dosage,
    };

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Medication Reminder',
      message.notification?.body ?? 'Time to take $medicationName${dosage.isNotEmpty ? ' ($dosage)' : ''}',
      platformChannelSpecifics,
      payload: payloadData.toString(),
    );

    log('Medication notification shown with payload: $payloadData');
  }

  /// Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    log('Notification tapped: ${message.messageId}');
    log('Notification data: ${message.data}');

    // Handle navigation based on notification data
    _navigateBasedOnNotification(message.data);
  }

  /// Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    log('Local notification tapped: ${response.payload}');
    log('Action ID: ${response.actionId}');

    // Handle action button responses
    if (response.actionId != null) {
      _handleNotificationAction(response);
    } else {
      // Handle regular notification tap
      _handleNotificationNavigation(response.payload);
    }
  }

  /// Handle notification action button responses
  void _handleNotificationAction(NotificationResponse response) {
    try {
      // Parse the payload to get medication details
      final payload = response.payload;
      if (payload == null) return;

      // Parse the data (assuming it's in a format we can parse)
      log('Handling notification action: ${response.actionId}');
      log('Payload: $payload');

      // Extract medication details from payload
      final medicationName = _extractMedicationNameFromPayload(payload);
      final time = _extractTimeFromPayload(payload);

      log('Extracted medication name: $medicationName');
      log('Extracted time: $time');

      if (medicationName.isNotEmpty && time.isNotEmpty) {
        switch (response.actionId) {
          case 'taken_action':
            _recordMedicationTaken(medicationName, time);
            // Dismiss the notification
            _dismissNotification(response.id);
            break;
          case 'skip_action':
            _recordMedicationSkipped(medicationName, time);
            // Dismiss the notification
            _dismissNotification(response.id);
            break;
        }
      } else {
        log('Failed to extract medication details from payload');
      }
    } catch (e) {
      log('Error handling notification action: $e');
    }
  }

  /// Dismiss a specific notification
  void _dismissNotification(int? notificationId) {
    if (notificationId != null) {
      _localNotifications.cancel(notificationId);
      log('Notification dismissed: $notificationId');
    }
  }

  /// Handle notification navigation
  void _handleNotificationNavigation(String? payload) {
    if (payload == null) return;

    try {
      log('Handling notification navigation with payload: $payload');

      // Parse payload to determine navigation
      if (payload.contains('medication_reminder') || payload.contains('type: medication_reminder')) {
        log('Navigating to medication screen from payload');
        // Add a small delay to ensure the app is fully loaded
        Future.delayed(const Duration(milliseconds: 500), () {
          NavigationService.navigateToMedicationScreen();
        });
      }
    } catch (e) {
      log('Error handling notification navigation: $e');
    }
  }

  /// Record medication as taken
  Future<void> _recordMedicationTaken(String medicationName, String time) async {
    try {
      log('Recording medication as taken: $medicationName at $time');

      // Use FirestoreService directly to avoid provider issues in background
      final firestoreService = FirestoreService();
      final now = DateTime.now();
      final date = DateFormat('dd-MM-yyyy').format(now);

      final intake = MedicationIntake(
        medicationName: medicationName,
        date: date,
        time: time,
        status: 'taken',
        timestamp: Timestamp.now(),
        notes: 'Marked as taken from notification',
      );

      await firestoreService.recordMedicationIntake(intake);
      log('Medication marked as taken successfully');
    } catch (e) {
      log('Error recording medication as taken: $e');
    }
  }

  /// Record medication as skipped
  Future<void> _recordMedicationSkipped(String medicationName, String time) async {
    try {
      log('Recording medication as skipped: $medicationName at $time');

      // Use FirestoreService directly to avoid provider issues in background
      final firestoreService = FirestoreService();
      final now = DateTime.now();
      final date = DateFormat('dd-MM-yyyy').format(now);

      final intake = MedicationIntake(
        medicationName: medicationName,
        date: date,
        time: time,
        status: 'skipped',
        timestamp: Timestamp.now(),
        notes: 'Marked as skipped from notification',
      );

      await firestoreService.recordMedicationIntake(intake);
      log('Medication marked as skipped successfully');
    } catch (e) {
      log('Error recording medication as skipped: $e');
    }
  }

  /// Extract medication name from payload
  String _extractMedicationNameFromPayload(String payload) {
    try {
      log('Parsing payload for medication name: $payload');

      // Simple comma-separated format: medication_name:value,time:value
      final regex = RegExp(r'medication_name:([^,]+)');
      final match = regex.firstMatch(payload);
      if (match != null) {
        return match.group(1)?.trim() ?? '';
      }

      // Fallback: try Map format
      final regex2 = RegExp(r'medication_name:\s*([^,}]+)');
      final match2 = regex2.firstMatch(payload);
      return match2?.group(1)?.trim() ?? '';
    } catch (e) {
      log('Error extracting medication name: $e');
      return '';
    }
  }

  /// Extract time from payload
  String _extractTimeFromPayload(String payload) {
    try {
      log('Parsing payload for time: $payload');

      // Simple comma-separated format: medication_name:value,time:value
      final regex = RegExp(r'time:([^,]+)');
      final match = regex.firstMatch(payload);
      if (match != null) {
        return match.group(1)?.trim() ?? '';
      }

      // Fallback: try Map format
      final regex2 = RegExp(r'time:\s*([^,}]+)');
      final match2 = regex2.firstMatch(payload);
      return match2?.group(1)?.trim() ?? '';
    } catch (e) {
      log('Error extracting time: $e');
      return '';
    }
  }

  /// Navigate based on notification data
  void _navigateBasedOnNotification(Map<String, dynamic> data) {
    // Implement navigation logic based on notification type
    String? type = data['type'];

    log('Navigating based on notification type: $type');

    switch (type) {
      case 'medication_reminder':
        log('Navigate to medication screen');
        // Add a small delay to ensure the app is fully loaded
        Future.delayed(const Duration(milliseconds: 500), () {
          NavigationService.navigateToMedicationScreen();
        });
        break;
      case 'health_update':
        log('Navigate to health screen');
        // Add navigation logic here for health updates
        break;
      default:
        log('Unknown notification type: $type');
    }
  }

  /// Get and store FCM token
  Future<void> _handleTokenRefresh() async {
    try {
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        log('FCM Token: $token');
        await _storeFCMToken(token);
      } else {
        log('Failed to get FCM token');
      }
    } catch (e) {
      log('Error getting FCM token: $e');
    }
  }

  /// Handle token refresh
  Future<void> _onTokenRefresh(String token) async {
    log('FCM Token refreshed: $token');
    await _storeFCMToken(token);
  }

  /// Store FCM token in Firestore
  Future<void> _storeFCMToken(String token) async {
    try {
      // Update user document with FCM token
      await _firestoreService.updateFCMToken(token);
      log('FCM token stored successfully');
    } catch (e) {
      log('Error storing FCM token: $e');
    }
  }

  /// Get current FCM token
  Future<String?> getToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      log('Error getting current FCM token: $e');
      return null;
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      log('Subscribed to topic: $topic');
    } catch (e) {
      log('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      log('Unsubscribed from topic: $topic');
    } catch (e) {
      log('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
    log('All notifications cleared');
  }

  /// Show medication reminder notification with action buttons
  Future<void> showMedicationReminder({
    required String medicationName,
    required String time,
    String? dosage,
  }) async {
    final notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'medication_reminders',
      'Medication Reminders',
      channelDescription: 'Notifications for medication reminders with action buttons',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      fullScreenIntent: true,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'taken_action',
          'Taken',
          icon: DrawableResourceAndroidBitmap('ic_check'),
          contextual: false, // Set to false for background visibility
          showsUserInterface: false,
        ),
        AndroidNotificationAction(
          'skip_action',
          'Skip',
          icon: DrawableResourceAndroidBitmap('ic_close'),
          contextual: false, // Set to false for background visibility
          showsUserInterface: false,
        ),
      ],
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      categoryIdentifier: 'medication_reminder',
    );

    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Create payload with medication details in JSON format
    final payloadMap = {
      'type': 'medication_reminder',
      'medication_name': medicationName,
      'time': time,
      'dosage': dosage ?? '',
    };
    final payload = payloadMap.toString();

    await _localNotifications.show(
      notificationId,
      'Medication Reminder',
      'Time to take $medicationName${dosage != null ? ' ($dosage)' : ''}',
      platformChannelSpecifics,
      payload: payload,
    );

    log('Medication reminder shown: $medicationName at $time with payload: $payloadMap');
  }

  /// Schedule local medication reminder (THIS ALWAYS WORKS)
  Future<void> scheduleLocalMedicationReminder({
    required String medicationName,
    required DateTime scheduledTime,
    String? dosage,
  }) async {
    final notificationId = scheduledTime.millisecondsSinceEpoch ~/ 1000;
    final timeString = "${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}";

    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'medication_reminders',
      'Medication Reminders',
      channelDescription: 'Local medication reminders with action buttons',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      category: AndroidNotificationCategory.reminder,
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'taken_action',
          'Taken',
          icon: DrawableResourceAndroidBitmap('ic_check'),
        ),
        AndroidNotificationAction(
          'skip_action',
          'Skip',
          icon: DrawableResourceAndroidBitmap('ic_close'),
        ),
      ],
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Simple payload format that WORKS
    final payload = "medication_name:$medicationName,time:$timeString,dosage:${dosage ?? ''}";

    // Use immediate notification instead of scheduled for testing
    await _localNotifications.show(
      notificationId,
      'Medication Reminder',
      'Time to take $medicationName${dosage != null ? ' ($dosage)' : ''}',
      platformChannelSpecifics,
      payload: payload,
    );

    log('Local medication reminder scheduled: $medicationName at $timeString');
  }

  /// Test method to verify payload parsing
  void testPayloadParsing() {
    final testPayload = "medication_name:Test Medicine,time:09:00,dosage:1 tablet";
    log('Testing payload parsing with: $testPayload');

    final medicationName = _extractMedicationNameFromPayload(testPayload);
    final time = _extractTimeFromPayload(testPayload);

    log('Extracted medication name: $medicationName');
    log('Extracted time: $time');

    if (medicationName.isNotEmpty && time.isNotEmpty) {
      log('Payload parsing test: SUCCESS');
    } else {
      log('Payload parsing test: FAILED');
    }
  }

  /// Test local notification with action buttons (GUARANTEED TO WORK)
  Future<void> testLocalNotificationWithActions() async {
    final now = DateTime.now().add(const Duration(seconds: 5));
    await scheduleLocalMedicationReminder(
      medicationName: "Test Medicine",
      scheduledTime: now,
      dosage: "1 tablet",
    );
    log('Test local notification scheduled for 5 seconds from now');
  }
}
