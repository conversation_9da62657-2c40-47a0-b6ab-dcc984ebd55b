import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/route/route_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/medication_intake_provider.dart';
import 'package:healo/services/notification_service.dart';
import 'package:intl/intl.dart';

class MedicationListScreen extends ConsumerStatefulWidget {
  const MedicationListScreen({super.key});

  @override
  ConsumerState<MedicationListScreen> createState() =>
      _MedicationListScreenState();
}

class _MedicationListScreenState extends ConsumerState<MedicationListScreen> {
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(medicationProvider.notifier).fetchDailyMedicationHistory();
      ref
          .read(weeklyMedicationProvider.notifier)
          .fetchWeeklyMedicationHistory();
      ref
          .read(monthlyMedicationProvider.notifier)
          .fetchMonthlyMedicationHistory();
    });
  }

  @override
  Widget build(BuildContext context) {
    final weeklyMedications = ref.watch(weeklyMedicationProvider);
    final dailyMedications = ref.watch(medicationProvider);
    final monthlyMedications = ref.watch(monthlyMedicationProvider);

    final selected = _selectedDate ?? DateTime.now();
    final dateRange = List.generate(5, (i) {
      return selected.subtract(Duration(days: 2 - i));
    });

    List<DailyMedication> filteredDaily = dailyMedications.where((med) {
      try {
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        return expiry.isAfter(selected);
      } catch (_) {
        return true;
      }
    }).toList();

    List<WeeklyMedication> filteredWeekly = weeklyMedications.where((med) {
      try {
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        final isNotExpired = expiry.isAfter(selected);
        final weekday = DateFormat('EEEE').format(selected);
        final isScheduledToday = med.days.contains(weekday);

        return isNotExpired && isScheduledToday;
      } catch (_) {
        return false;
      }
    }).toList();

    List<MonthlyMedication> filteredMonthly = monthlyMedications.where((med) {
      try {
        // Parse expiry
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        if (!expiry.isAfter(selected)) return false;

        final selectedDay = selected.day;

        final isScheduledToday = med.dates.any((dateStr) {
          final parts = dateStr?.split('/');
          final day = int.tryParse(parts![0]);
          return day == selectedDay;
        });

        return isScheduledToday;
      } catch (_) {
        return false;
      }
    }).toList();

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Medication",
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () async {
              // Test notification with action buttons
              final now = DateTime.now();
              final currentTime = "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}";
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              await NotificationService().showMedicationReminder(
                medicationName: "Test Medicine",
                time: currentTime,
                dosage: "1 tablet",
              );

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Test notification sent! Check your notification panel.'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              _showIntakeHistory();
            },
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              // Test payload parsing
              NotificationService().testPayloadParsing();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Payload parsing test run - check console logs'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ),
      body: dailyMedications.isEmpty &&
              weeklyMedications.isEmpty &&
              monthlyMedications.isEmpty
          ? const Center(child: Text("No medications found"))
          : Padding(
              padding: EdgeInsets.only(
                  left: MySize.size15,
                  right: MySize.size15,
                  bottom: MySize.size85),
              child: ListView(
                scrollDirection: Axis.vertical,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _monthName(_selectedDate!.month),
                        style: TextStyle(
                          fontSize: MySize.size22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.calendar_today,
                            color: AppColors.primaryColor),
                        onPressed: () async {
                          final pickedDate = await showDatePicker(
                            context: context,
                            initialDate: _selectedDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );

                          if (pickedDate != null) {
                            setState(() {
                              _selectedDate = pickedDate;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MySize.size74,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: dateRange.map((date) {
                        return Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedDate = date;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: MySize.size5),
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: AppColors.primaryColor),
                                color: _isSameDate(date, _selectedDate!)
                                    ? AppColors.primaryColor
                                    : Theme.of(context).scaffoldBackgroundColor,
                                borderRadius:
                                    BorderRadius.circular(MySize.size20),
                              ),
                              padding:
                                  EdgeInsets.symmetric(vertical: MySize.size12),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _weekdayShort(date),
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontSize: MySize.size12,
                                    ),
                                  ),
                                  Text(
                                    "${date.day}",
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Space.height(12),
                  Text(
                    "Today's Medicine",
                    style: TextStyle(
                      fontSize: MySize.size22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (filteredDaily.isNotEmpty) ...[
                    ...filteredDaily.map((med) => Dismissible(
                          key: ValueKey(med.medicineName),
                          background: Container(
                            alignment: Alignment.centerRight,
                            decoration: BoxDecoration(
                                color: AppColors.red,
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            padding:
                                EdgeInsets.symmetric(horizontal: MySize.size20),
                            child: const Icon(Icons.delete,
                                color: AppColors.white),
                          ),
                          direction: DismissDirection.endToStart,
                          confirmDismiss: (direction) async {
                            return await showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                backgroundColor: Theme.of(context).cardColor,
                                title: Text("Confirm",
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color)),
                                content: const Text(
                                    "Are you sure you want to delete this medication?"),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(false),
                                    child: const Text(
                                      "Cancel",
                                      style: TextStyle(
                                          color: AppColors.primaryColor),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(true),
                                    child: const Text("Delete",
                                        style: TextStyle(color: Colors.red)),
                                  ),
                                ],
                              ),
                            );
                          },
                          onDismissed: (direction) async {
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            await ref
                                .read(medicationProvider.notifier)
                                .deleteDailyMedication(med.medicineName);
                          },
                          child: Card(
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            elevation: 0,
                            color: Theme.of(context).cardColor,
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            child: Padding(
                              padding: EdgeInsets.all(MySize.size12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Title
                                  Text(
                                    "${med.medicineName} ${med.quantity}${med.unit}",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: MySize.size16,
                                      color: Theme.of(context).textTheme.bodyMedium?.color,
                                    ),
                                  ),
                                  Space.height(8),
                                  // Subtitle
                                  Row(
                                    children: [
                                      Text("${med.dosage} ",
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Text(med.frequency,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Space.width(5),
                                      Icon(Icons.circle,
                                          size: MySize.size10,
                                          color: AppColors.primaryColor),
                                      Space.width(5),
                                      Text(med.mealTime,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                    ],
                                  ),
                                  Space.height(12),
                                  // Action buttons at bottom
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: () async {
                                            // Mark as taken for the first timing
                                            if (med.timing.isNotEmpty) {
                                              final scaffoldMessenger = ScaffoldMessenger.of(context);
                                              await ref
                                                  .read(medicationIntakeProvider.notifier)
                                                  .markMedicationTaken(med.medicineName, med.timing.first);
                                              if (mounted) {
                                                scaffoldMessenger.showSnackBar(
                                                  SnackBar(content: Text('${med.medicineName} marked as taken')),
                                                );
                                              }
                                            }
                                          },
                                          icon: const Icon(Icons.check, size: 16),
                                          label: const Text('Taken', style: TextStyle(fontSize: 12)),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.green,
                                            foregroundColor: Colors.white,
                                            padding: EdgeInsets.symmetric(vertical: MySize.size8),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(MySize.size8),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Space.width(8),
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: () async {
                                            // Mark as skipped for the first timing
                                            if (med.timing.isNotEmpty) {
                                              final scaffoldMessenger = ScaffoldMessenger.of(context);
                                              await ref
                                                  .read(medicationIntakeProvider.notifier)
                                                  .markMedicationSkipped(med.medicineName, med.timing.first);
                                              if (mounted) {
                                                scaffoldMessenger.showSnackBar(
                                                  SnackBar(content: Text('${med.medicineName} marked as skipped')),
                                                );
                                              }
                                            }
                                          },
                                          icon: const Icon(Icons.close, size: 16),
                                          label: const Text('Skip', style: TextStyle(fontSize: 12)),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.orange,
                                            foregroundColor: Colors.white,
                                            padding: EdgeInsets.symmetric(vertical: MySize.size8),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(MySize.size8),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Space.width(8),
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: () {
                                            Navigator.pushNamed(
                                                context, addmedicationScreen,
                                                arguments: med);
                                          },
                                          icon: const Icon(Icons.edit, size: 16),
                                          label: const Text('Edit', style: TextStyle(fontSize: 12)),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.blue,
                                            foregroundColor: Colors.white,
                                            padding: EdgeInsets.symmetric(vertical: MySize.size8),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(MySize.size8),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )),
                  ],
                  if (filteredWeekly.isNotEmpty) ...[
                    ...filteredWeekly.map((med) => Dismissible(
                          key: ValueKey(med.medicineName),
                          background: Container(
                            alignment: Alignment.centerRight,
                            decoration: BoxDecoration(
                                color: AppColors.red,
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            padding:
                                EdgeInsets.symmetric(horizontal: MySize.size20),
                            child: const Icon(Icons.delete,
                                color: AppColors.white),
                          ),
                          direction: DismissDirection.endToStart,
                          confirmDismiss: (direction) async {
                            return await showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                backgroundColor: Theme.of(context).cardColor,
                                title: Text("Confirm",
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color)),
                                content: const Text(
                                    "Are you sure you want to delete this medication?"),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(false),
                                    child: const Text(
                                      "Cancel",
                                      style: TextStyle(
                                          color: AppColors.primaryColor),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(true),
                                    child: const Text("Delete",
                                        style: TextStyle(color: Colors.red)),
                                  ),
                                ],
                              ),
                            );
                          },
                          onDismissed: (direction) async {
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            await ref
                                .read(weeklyMedicationProvider.notifier)
                                .deleteWeeklyMedication(med.medicineName);
                          },
                          child: Card(
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            elevation: 0,
                            color: Theme.of(context).cardColor,
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            child: ListTile(
                              title: Text(
                                  "${med.medicineName} ${med.quantity}${med.unit}",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color)),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text("${med.dosage} ",
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Text(med.frequency,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Space.width(5),
                                      Icon(Icons.circle,
                                          size: MySize.size10,
                                          color: AppColors.primaryColor),
                                      Space.width(5),
                                      Text(med.mealTime,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: IconButton(
                                icon:
                                    const Icon(Icons.edit, color: Colors.blue),
                                onPressed: () {
                                  Navigator.pushNamed(
                                      context, addmedicationScreen,
                                      arguments: med);
                                },
                              ),
                            ),
                          ),
                        )),
                  ],
                  if (filteredMonthly.isNotEmpty) ...[
                    ...filteredMonthly.map((med) => Dismissible(
                          key: ValueKey(med.medicineName),
                          background: Container(
                            alignment: Alignment.centerRight,
                            decoration: BoxDecoration(
                                color: AppColors.red,
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            padding:
                                EdgeInsets.symmetric(horizontal: MySize.size20),
                            child: const Icon(Icons.delete,
                                color: AppColors.white),
                          ),
                          direction: DismissDirection.endToStart,
                          confirmDismiss: (direction) async {
                            return await showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                backgroundColor: Theme.of(context).cardColor,
                                title: Text("Confirm",
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color)),
                                content: const Text(
                                    "Are you sure you want to delete this medication?"),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(false),
                                    child: const Text(
                                      "Cancel",
                                      style: TextStyle(
                                          color: AppColors.primaryColor),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(true),
                                    child: const Text("Delete",
                                        style: TextStyle(color: Colors.red)),
                                  ),
                                ],
                              ),
                            );
                          },
                          onDismissed: (direction) async {
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            await ref
                                .read(monthlyMedicationProvider.notifier)
                                .deleteMonthlyMedication(med.medicineName);
                          },
                          child: Card(
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(MySize.size10)),
                            elevation: 0,
                            color: Theme.of(context).cardColor,
                            margin: EdgeInsets.symmetric(
                                horizontal: MySize.size10,
                                vertical: MySize.size5),
                            child: ListTile(
                              title: Text(
                                  "${med.medicineName} ${med.quantity}${med.unit}",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color)),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text("${med.dosage} ",
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Text(med.frequency,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                      Space.width(5),
                                      Icon(Icons.circle,
                                          size: MySize.size10,
                                          color: AppColors.primaryColor),
                                      Space.width(5),
                                      Text(med.mealTime,
                                          style: TextStyle(
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color)),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: IconButton(
                                icon:
                                    const Icon(Icons.edit, color: Colors.blue),
                                onPressed: () {
                                  Navigator.pushNamed(
                                      context, addmedicationScreen,
                                      arguments: med);
                                },
                              ),
                            ),
                          ),
                        )),
                  ]
                ],
              ),
            ),
      floatingActionButton: SizedBox(
          height: MediaQuery.of(context).size.width * 0.15,
          width: MediaQuery.of(context).size.width / 2,
          child: CustomButton(
              onTap: () {
                Navigator.pushNamed(context, addmedicationScreen);
              },
              text: "Add Medication")),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  String _monthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  bool _isSameDate(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;

  String _weekdayShort(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }

  /// Show medication intake history dialog
  void _showIntakeHistory() async {
    try {
      // Fetch all intake records
      await ref.read(medicationIntakeProvider.notifier).fetchAllIntakes();
      final intakes = ref.read(medicationIntakeProvider);

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: Text(
            'Medication Intake History',
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: intakes.isEmpty
                ? const Center(child: Text('No intake records found'))
                : ListView.builder(
                    itemCount: intakes.length,
                    itemBuilder: (context, index) {
                      final intake = intakes[index];
                      return Card(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        child: ListTile(
                          title: Text(
                            intake.medicationName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                          subtitle: Text(
                            '${intake.date} at ${intake.time}',
                            style: TextStyle(
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: intake.status == 'taken'
                                  ? Colors.green
                                  : intake.status == 'skipped'
                                      ? Colors.orange
                                      : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              intake.status.toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Close',
                style: TextStyle(color: AppColors.primaryColor),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading intake history: $e')),
        );
      }
    }
  }
}
